# SRE API 服务工程模板

这是一个专为 SRE（Site Reliability Engineering）设计的 API 服务工程模板，采用函数式编程风格和模块化架构。

## 🎯 核心原则

### 1. 函数式编程风格
- **纯函数编程**：不需要冗余的类抽象，业务逻辑全部使用纯函数实现
- **模块化路由**：使用 proxy_handler.py 中的 proxy_map 来模块化不同的 API 路由
- **统一接口**：所有资源模块仅实现 list 和 update 接口
- **参数传递**：函数签名传递一个 dict 来支持不同的业务场景，参数按需取

### 2. 存储架构设计
- **Starrocks 为主**：没有事务要求的存储需求使用 Starrocks
  - 主键类型：数据规模可控，有对一行数据有修改的需求
  - 时间序列表：海量数据，根据时间分区，不支持行级更新
- **MySQL + ORM**：有事务要求使用 MySQL + tortoise-orm
- **统一装饰器**：使用 storage + aiomysql 来操作 Starrocks

### 3. 代码组织规范
- **单文件限制**：每个文件不超过 500 行
- **测试统一**：测试用例统一放到 tests 目录下
- **文档管理**：非 README.MD 的 MD 文档都放到 docs 下

## 📁 项目结构

```
app/
├── __init__.py              # 应用工厂，使用 Sanic 框架
├── config.py               # 配置管理
├── app.sql                 # Starrocks 存储模型管理
├── models.py               # MySQL 模型定义（使用 tortoise-orm）
├── proxy_handler.py        # 模块代理管理器，完成模块注册
├── views.py                # API 路由定义，使用 proxy_map 调用
├── utils.py                # 工具函数
├── tasks.py                # 后台任务定义
├── storage.py              # Starrocks 存储装饰器
└── modules/                # 业务模块目录
    ├── __init__.py
    └── example_module/     # 示例业务模块
        ├── __init__.py
        └── handler.py      # 业务函数实现（list 和 update）
tests/                      # 测试用例目录
docs/                       # 文档目录
```

## 🚀 快速开始

### 1. 存储选型判断

根据业务需求选择合适的存储：

**使用 Starrocks（推荐）**：
- 没有事务要求的存储需求
- 在 `app/app.sql` 中管理存储模型
- 主键表：Table 描述中有「PRIMARY KEY」，面向行级更新
- 时间序列表：Table 描述中有「DUPLICATE KEY」，面向时间序列海量数据

**使用 MySQL + ORM**：
- 有事务要求的业务场景
- 在 `app/models.py` 中定义 class 描述，使用 tortoise-orm

### 2. 创建新模块

```bash
mkdir app/modules/your_module
touch app/modules/your_module/__init__.py
touch app/modules/your_module/handler.py
```

### 3. 实现业务函数

```python
# app/modules/your_module/handler.py
from app.storage import storage
import aiomysql

@storage
async def list(params: dict) -> dict:
    """列出项目 - 仅实现 list 接口"""
    # 从 params 中按需取参数
    category = params.get('category')

    # 使用 SQL 和 aiomysql 与 Starrocks 交互
    query = "SELECT * FROM items WHERE category = %s"
    result = await execute_query(query, (category,))

    # 直接返回 data 内容，不需要包装 response 格式
    return result

@storage
async def update(params: dict) -> dict:
    """更新项目 - 仅实现 update 接口"""
    item_id = params.get('id', 0)

    if item_id == 0:
        # 生成唯一 ID 并创建对象
        item_id = generate_unique_id()
        query = "INSERT INTO items (id, name, category) VALUES (%s, %s, %s)"
    else:
        # 更新对象（Starrocks 主键表执行 insert 即可完成更新）
        query = "INSERT INTO items (id, name, category) VALUES (%s, %s, %s)"

    result = await execute_query(query, (item_id, params.get('name'), params.get('category')))
    return {"id": item_id}
```

### 4. 注册模块

```python
# app/proxy_handler.py
from .modules.your_module import handler as your_handler

proxy_map = {
    "your_module": {
        "list": your_handler.list_items,
        "update": your_handler.update_items,
    }
}
```

### 5. 添加 API 路由

```python
# app/views.py
from sanic import json
from .proxy_handler import proxy_map

@app.route('/<module>/<operation>', methods=['GET', 'POST'])
async def api_handler(request, module, operation):
    try:
        # 根据 GET 和 POST 提取参数
        if request.method == 'GET':
            params = dict(request.args)
        else:
            params = request.json or {}

        # 调用 proxy_map
        data = await proxy_map[module][operation](params)

        response = {
            "data": data,
            "success": True,
            "message": ""
        }
    except Exception as e:
        response = {
            "data": {},
            "success": False,
            "message": str(e)
        }

    return json(response)
```

## 📋 接口规范

### 业务函数规范
所有资源模块仅实现两个接口：
- `list_xxx(params: dict) -> dict` - 列表查询接口
- `update_xxx(params: dict) -> dict` - 更新/创建接口

### 参数处理规则
- 函数签名传递一个 dict 来支持不同的业务场景
- 参数按需从 dict 中取值
- 如果数据的唯一 ID 是 0 则生成唯一 ID 并创建对象，否则更新对象

### 响应格式统一

```python
# API 响应格式（在 views.py 中统一处理）
response = {
    "data": {},      # list 和 update 函数直接返回的内容
    "success": True, # 成功标识
    "message": ""    # 错误信息（异常时设置）
}

# 业务函数只需返回 data 内容，不需要包装 response 格式
# 异常处理在调用 proxy_map 之前统一处理
```

## 🔧 存储操作

### Starrocks 操作（推荐）

```python
# 使用统一装饰器 storage + aiomysql 操作 Starrocks
from app.storage import storage
import aiomysql

@storage
async def list_data(params: dict):
    # 直接用 SQL 和 aiomysql 与存储交互
    query = "SELECT * FROM your_table WHERE condition = %s"
    result = await execute_query(query, (params.get('condition'),))
    return result

@storage
async def update_data(params: dict):
    # 主键表执行 insert 即可完成创建和更新，不需要 update
    query = "INSERT INTO your_table (id, name, value) VALUES (%s, %s, %s)"
    await execute_query(query, (params.get('id'), params.get('name'), params.get('value')))
    return {"id": params.get('id')}
```

### MySQL + ORM 操作（有事务需求时）

```python
# app/models.py 中定义模型
from tortoise.models import Model
from tortoise import fields

class User(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50)
    email = fields.CharField(max_length=100)

# 在业务函数中使用 ORM
async def list_users(params: dict):
    users = await User.all()
    return [{"id": u.id, "name": u.name, "email": u.email} for u in users]
```

### 存储模型定义

```sql
-- app/app.sql - Starrocks 存储模型管理

-- 主键表（支持行级更新）
CREATE TABLE user_profiles (
    id BIGINT NOT NULL,
    name VARCHAR(100),
    email VARCHAR(100),
    updated_at DATETIME
) ENGINE=OLAP
PRIMARY KEY (id)  -- 主键类型，数据规模可控，有对一行数据有修改的需求
DISTRIBUTED BY HASH(id);

-- 时间序列表（海量数据，不支持行级更新）
CREATE TABLE user_events (
    event_id BIGINT NOT NULL,
    user_id BIGINT,
    event_type VARCHAR(50),
    event_time DATETIME,
    data JSON
) ENGINE=OLAP
DUPLICATE KEY (event_id, user_id, event_time)  -- 时间序列表，根据时间分区
PARTITION BY RANGE(event_time);
```

## ✅ 最佳实践

### 1. 函数设计原则
- 纯函数编程，不需要冗余的类抽象
- 每个函数只做一件事
- 使用类型注解：`params: dict -> dict`
- 业务函数不需要任何异常处理逻辑

### 2. 错误处理
```python
# 业务函数中不需要异常处理，直接实现业务逻辑
async def list_items(params: dict) -> dict:
    # 直接实现业务逻辑，不需要 try-catch
    result = await execute_query("SELECT * FROM items", ())
    return result

# 异常处理在 views.py 中调用 proxy_map 之前统一处理
@app.route('/<module>/<operation>', methods=['GET', 'POST'])
async def api_handler(request, module, operation):
    try:
        data = await proxy_map[module][operation](params)
        response = {"data": data, "success": True, "message": ""}
    except Exception as e:
        response = {"data": {}, "success": False, "message": str(e)}
    return json(response)
```

### 3. 代码组织
```python
# 适当的逻辑拆分，单个文件代码行数不超过 500 行
# 在 proxy_handler.py 完成注册

# proxy_handler.py
from .modules.user import handler as user_handler
from .modules.order import handler as order_handler

proxy_map = {
    "user": {
        "list": user_handler.list_users,
        "update": user_handler.update_users,
    },
    "order": {
        "list": order_handler.list_orders,
        "update": order_handler.update_orders,
    }
}
```

### 4. 后台任务
```python
# tasks.py 里面定义后台任务
from sanic import Sanic

async def cleanup_old_data():
    """清理过期数据的后台任务"""
    # 后台任务逻辑
    pass

def register_tasks(app: Sanic):
    """注册后台任务"""
    app.add_task(cleanup_old_data())
```

## 🧪 测试建议

### 单元测试
```python
# tests/ 目录下统一放置测试用例
import pytest
from app.modules.your_module.handler import list_items, update_items

@pytest.mark.asyncio
async def test_list_items():
    params = {"category": "test"}
    result = await list_items(params)
    assert isinstance(result, dict)
    assert len(result) >= 0

@pytest.mark.asyncio
async def test_update_items():
    params = {"id": 0, "name": "test_item", "category": "test"}
    result = await update_items(params)
    assert "id" in result
    assert result["id"] != 0
```

### API 测试
```python
async def test_api_endpoint():
    # 测试 Sanic API 端点
    request, response = app.test_client.get('/user/list')
    assert response.status == 200
    assert response.json["success"] is True
    assert "data" in response.json
```

## 📝 开发流程

1. **存储选型** - 根据是否需要事务选择 Starrocks 或 MySQL
2. **定义存储模型** - 在 app.sql 或 models.py 中定义
3. **创建业务模块** - 实现 list 和 update 两个接口
4. **注册模块** - 在 proxy_handler.py 中完成注册
5. **路由配置** - views.py 中使用统一的路由模式
6. **编写测试** - 在 tests/ 目录下添加测试用例

## 🔍 技术栈

- **Web 框架**：Sanic Python API 框架
- **主存储**：Starrocks（使用 aiomysql 连接）
- **事务存储**：MySQL + tortoise-orm
- **架构模式**：函数式编程 + 模块化路由
- **测试框架**：pytest + asyncio

## 📋 目录规范

- `tests/` - 测试用例统一放置目录
- `docs/` - 非 README.MD 的 MD 文档放置目录
- `app/app.sql` - Starrocks 存储模型管理
- `app/models.py` - MySQL 模型定义（使用 tortoise-orm）
- `app/proxy_handler.py` - 模块注册和 proxy_map 定义
- `app/tasks.py` - 后台任务定义

---

**核心理念：纯函数编程 + 模块化路由 + 统一存储接口**
