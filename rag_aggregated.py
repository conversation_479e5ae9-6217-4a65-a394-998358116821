#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG功能聚合文件
包含知识库相关的所有API调用功能
"""
import os
import json
import requests
from volcengine.auth.SignerV4 import SignerV4
from volcengine.base.Request import Request
from volcengine.Credentials import Credentials


class RAGAPI:
    def __init__(self, ak, sk):
        self.ak = ak
        self.sk = sk
        self.domain = "api-knowledgebase.mlp.cn-beijing.volces.com"

    def prepare_request(self, method, path, params=None, data=None, doseq=0):
        if params:
            for key in params:
                if isinstance(params[key], (int, float, bool)):
                    params[key] = str(params[key])
                elif isinstance(params[key], list) and not doseq:
                    params[key] = ",".join(params[key])

        r = Request()
        r.set_shema("http")
        r.set_method(method)
        r.set_connection_timeout(10)
        r.set_socket_timeout(10)

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Host": self.domain,
        }
        r.set_headers(headers)

        if params:
            r.set_query(params)

        r.set_host(self.domain)
        r.set_path(path)

        if data is not None:
            r.set_body(json.dumps(data))

        credentials = Credentials(self.ak, self.sk, "air", "cn-north-1")
        SignerV4.sign(r, credentials)

        return r

    def make_request(self, method, path, data=None, params=None):
        try:
            info_req = self.prepare_request(
                method=method, path=path, data=data, params=params
            )

            response = requests.request(
                method=info_req.method,
                url="https://{}{}".format(self.domain, info_req.path),
                headers=info_req.headers,
                data=info_req.body,
            )

            # print(f"请求URL: https://{self.domain}{info_req.path}")
            # print(f"请求方法: {info_req.method}")
            # print(f"请求头: {info_req.headers}")
            # print(f"请求体: {info_req.body}")
            # print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")

            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            print(f"请求异常: {str(e)}")
            return None

    def get_point_info(self, resource_id, point_id):
        """获取知识库片段信息"""
        method = "POST"
        path = "/api/knowledge/point/info"
        data = {"resource_id": resource_id, "point_id": point_id}
        print("=== 获取知识库片段信息 ===")
        return self.make_request(method, path, data)

    def get_collection_info(self, resource_id):
        """获取知识库信息"""
        method = "POST"
        path = "/api/knowledge/collection/info"
        data = {"resource_id": resource_id}
        print("=== 获取知识库信息 ===")
        return self.make_request(method, path, data)

    def update_document(self, resource_id, doc_id):
        """更新文档"""
        method = "POST"
        path = "/api/knowledge/doc/update"
        data = {"resource_id": resource_id, "doc_id": doc_id, "doc_name": "test"}
        print("=== 更新文档 ===")
        return self.make_request(method, path, data)

    def get_document_info(self, resource_id, doc_id):
        """获取文档信息"""
        method = "POST"
        path = "/api/knowledge/doc/info"
        data = {"resource_id": resource_id, "doc_id": doc_id}
        print("=== 获取文档信息 ===")
        return self.make_request(method, path, data)

    def update_collection(
        self, resource_id, description=None, cpu_quota=None, name=None, project=""
    ):
        """更新知识库信息"""
        method = "POST"
        path = "/api/knowledge/collection/update"

        data = {"resource_id": resource_id}

        if description is not None:
            data["description"] = description
        if cpu_quota is not None:
            data["cpu_quota"] = cpu_quota
        if name is not None:
            data["name"] = name
        if project:
            data["project"] = project

        print("=== 更新知识库信息 ===")
        return self.make_request(method, path, data)

    def add_point(self, resource_id, doc_id, chunk_type, content):
        """新增切片"""
        method = "POST"
        path = "/api/knowledge/point/add"
        data = {
            "resource_id": resource_id,
            "doc_id": doc_id,
            "chunk_type": chunk_type,
            "content": content,
        }
        print("=== 新增切片 ===")
        return self.make_request(method, path, data)

    # def delete_point(self, resource_id, point_id):
    #     """删除切片"""
    #     method = "POST"
    #     path = "/api/knowledge/point/delete"
    #     data = {"resource_id": resource_id, "point_id": point_id}
    #     print("=== 删除切片 ===")
    #     return self.make_request(method, path, data)

    def get_point_list(self, resource_id, doc_id):
        """获取切片列表"""
        method = "POST"
        path = "/api/knowledge/point/list"
        data = {"resource_id": resource_id, "doc_id": doc_id}
        print("=== 获取切片列表 ===")
        return self.make_request(method, path, data)

    def update_point(self, resource_id, point_id, content):
        """更新切片"""
        method = "POST"
        path = "/api/knowledge/point/update"
        data = {"resource_id": resource_id, "point_id": point_id, "content": content}
        print("=== 更新切片 ===")
        return self.make_request(method, path, data)

    def delete_point(self, resource_id, point_id):
        """删除切片"""
        method = "POST"
        path = "/api/knowledge/point/delete"
        data = {"resource_id": resource_id, "point_id": point_id}
        print("=== 删除切片 ===")
        return self.make_request(method, path, data)


def main():
    """主函数 - 示例用法"""
    ak = os.getenv("AK")
    sk = os.getenv("SK")

    rag_api = RAGAPI(ak, sk)

    resource_id = "kb-edf5eac2050d11a8"
    doc_id = "_sys_auto_gen_doc_id-8862279818656335569"
    point_id = "_sys_auto_gen_doc_id-8862279818656335569-0"

    print("开始测试RAG API功能...")
    print("=" * 60)

    # 1. 获取知识库信息
    print("\n1. 测试获取知识库信息")
    result1 = rag_api.get_collection_info(resource_id)
    print(f"结果: {'成功' if result1 else '失败'}")

    # 2. 获取文档信息
    print("\n2. 测试获取文档信息")
    result2 = rag_api.get_document_info(resource_id, doc_id)
    print(f"结果: {'成功' if result2 else '失败'}")

    # 3. 获取片段信息
    print("\n3. 测试获取片段信息")
    result3 = rag_api.get_point_info(resource_id, point_id)
    print(f"结果: {'成功' if result3 else '失败'}")

    # 4. 更新文档
    print("\n4. 测试更新文档")
    result4 = rag_api.update_document(resource_id, doc_id)
    print(f"结果: {'成功' if result4 else '失败'}")

    # 5. 更新知识库信息
    print("\n5. 测试更新知识库信息")
    result5 = rag_api.update_collection(
        resource_id=resource_id, description="这是一个测试知识库", cpu_quota=1
    )
    print(f"结果: {'成功' if result5 else '失败'}")

    print("\n所有测试完成!")

    # 6. 新增切片
    print("\n6. 测试新增切片")
    point_content = "测试新增切片"
    result6 = rag_api.add_point(
        resource_id, doc_id, chunk_type="text", content=point_content
    )
    print(f"结果: {'成功' if result6 else '失败'}")

    # 7. 切片列表
    print("\n7. 测试切片列表")
    result7 = rag_api.get_point_list(resource_id, doc_id)
    print(f"结果: {'成功' if result7 else '失败'}")

    # 8. 更新切片
    print("\n8. 测试更新切片")
    result8 = rag_api.update_point(resource_id, point_id, content="测试更新切片")
    print(f"结果: {'成功' if result8 else '失败'}")

    # 9. 删除切片
    print("\n9. 测试删除切片")
    result9 = rag_api.delete_point(resource_id, point_id)
    print(f"结果: {'成功' if result9 else '失败'}")


if __name__ == "__main__":
    main()
