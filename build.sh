#!/bin/bash
dir_name=agent-proxy
new_number=$(date +%Y%m%d%H%M%S)
current_branch=$(git symbolic-ref --short HEAD)
if [ "$current_branch" != "master" ]; then
    echo "当前不在master分支"
    exit 1
fi

if [ "$1" == "cn" ]; then
    workflow_name="quicksight.bytedance.net/${dir_name}"
elif [ "$1" == "i18n" ]; then
    workflow_name="quicksight-i18n.byteintl.net/${dir_name}"
else
    echo "请指定区域，cn 或 i18n"
    exit 1
fi 



image_name="${workflow_name}:v0.0.${new_number}"
docker build --no-cache -t $image_name . && docker push $image_name