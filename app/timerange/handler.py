"""
Example <PERSON><PERSON><PERSON> - 示例模块处理器
展示函数式编程风格的最佳实践

重要原则：
1. 使用纯函数，避免类的使用
2. 每个函数职责单一，易于测试
3. 保持代码简洁，单文件不超过500行
"""

import logging
from datetime import datetime
from ..utils import storage, serialize_datetime
from .utils import genrerated_id

logger = logging.getLogger(__name__)


@storage("cmdb")
async def list(db, cur, params):
    try:
        query_params = {params["start_timestamp"], params["end_timestamp"]}
        query = """
            SELECT timestamp, meta FROM timerange where timestamp >= %s and timestamp <= %s
        """
        await cur.execute(query, query_params)
        records = await cur.fetchall()

        # 转换为字典格式并处理 datetime 序列化
        columns = [desc[0] for desc in cur.description]
        result = []
        for record in records:
            row_dict = dict(zip(columns, record))
            result.append(row_dict)
        # 使用通用函数处理 datetime 序列化
        return serialize_datetime(result)
    except Exception as e:
        logger.error(f"Failed to list items: {e}")
        # 统一的错误响应格式
        return []


@storage("cmdb")
async def update(db, cur, params):
    item_data = params
    required_fields = ['id', 'meta']
    for field in required_fields:
        if field not in item_data:
            logger.error(f"{field} not in {item_data}")
            return {}
    

    if item_data['id'] in {0, "0", "", "undefined"}:
        _id = genrerated_id()
        query = """
            INSERT INTO timerange (`id`, meta)
            VALUES (%s, %s)
        """
    else:
    # 插入数据
        query = """
            INSERT INTO timerange (meta)
            VALUES (%s)
            WHERE id = %s
        """
    
    query_params = (
        item_data['id'],
        item_data['meta'],
    )
    
    await cur.execute(query, query_params)
    
    return {
        "id": item_data['id'],
        "meta": item_data['meta'],
    }
    