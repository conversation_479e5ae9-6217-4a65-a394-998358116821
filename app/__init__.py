"""
Application Factory - 应用工厂
展示如何创建和配置 Sanic 应用

重要原则：
1. 使用工厂模式创建应用
2. 模块化的蓝图注册
3. 统一的配置管理
4. 函数式编程风格的模块导入
"""

import traceback
from sanic import Sanic
from sanic.response import text, json
from tortoise.contrib.sanic import register_tortoise
from sanic.exceptions import MethodNotSupported

# 导入配置和任务
from .config import base
from .tasks import task_list

# 导入蓝图 - 注意这里导入了两个蓝图
from .views import gateway, api

# 可选：导入 proxy_handler 用于模块管理
from .proxy_handler import health_check, list_available_modules


def create_app():
    """
    创建 Sanic 应用实例

    注意：
    - 使用工厂模式，便于测试和部署
    - 模块化的蓝图注册
    - 统一的配置管理
    """
    app = Sanic(__name__)

    # 注册蓝图 - 支持多个蓝图和不同的URL前缀
    app.blueprint(gateway, url_prefix="/api/v1/alertman")  # 健康检查等基础接口
    app.blueprint(api, url_prefix="/api/v1")               # 业务API接口

    # 更新配置
    app.config.update(base)

    # 注册数据库（如果使用 Tortoise ORM）
    # 注意：如果使用原生SQL（推荐），可以注释掉这部分
    register_tortoise(
        app,
        db_url=app.config["DB"],
        modules={"models": ["app.models"]},
        generate_schemas=True
    )

    # 添加后台任务
    for task in task_list:
        app.add_task(task)

    # 添加调试端点（开发环境）
    @app.route('/debug/modules')
    async def debug_modules(request):
        """调试端点：查看所有可用模块"""
        return json({
            "modules": list_available_modules(),
            "health": health_check()
        })

    # 全局异常处理
    @app.exception(Exception)
    async def handle_exception(request, exception):
        """全局异常处理器"""
        return json({
            "success": False,
            "error": f"Internal server error: {str(exception)}",
            "type": type(exception).__name__
        }, status=500)

    return app


# 使用示例和最佳实践说明
"""
使用示例：

1. 开发环境启动：
   ```python
   from app import create_app
   app = create_app()
   app.run(host="0.0.0.0", port=8000, debug=True)
   ```

2. 生产环境启动：
   ```python
   from app import create_app
   app = create_app()
   # 使用 gunicorn 或其他 WSGI 服务器
   ```

3. 测试环境：
   ```python
   from app import create_app
   app = create_app()
   # 使用 app.test_client() 进行测试
   ```

蓝图组织最佳实践：

1. gateway 蓝图：基础功能
   - 健康检查
   - 系统状态
   - 调试接口

2. api 蓝图：业务功能
   - 业务API接口
   - CRUD操作
   - 复杂查询

3. 可以添加更多蓝图：
   - admin 蓝图：管理功能
   - auth 蓝图：认证功能
   - webhook 蓝图：回调接口

模块导入原则：
- 直接导入需要的函数，不导入类
- 在 views.py 中导入业务函数
- 在 __init__.py 中导入蓝图和配置
- 保持导入的简洁性和明确性
"""
