from datetime import datetime, timedelta
from functools import wraps
import aiomysql
import pytz

from config import base

beijing_tz = pytz.timezone('Asia/Shanghai')

def get_beijing_time(_datetime):
    return _datetime.astimezone(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')

def storage(_type):
    def decorate(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            db = None
            cursor = None
            try:
                if _type == "cmdb":
                    db = await aiomysql.connect(
                            host=base["SR_HOST"],
                            user=base["DB_USER"],
                            port=9030,
                            charset='utf8',
                            password=base["DB_PASSWD"],
                            db='cmdb',
                      
                        )
                elif _type == "monitor":
                    db = await aiomysql.connect(
                            host=base["SR_HOST"],
                            user=base["DB_USER"],
                            port=9030,
                            charset='utf8',
                            password=base["DB_PASSWD"],
                            db='monitor',
                        )
                else:
                    raise ValueError(f"Unsupported database type: {_type}")

                cursor = await db.cursor()
                res = await func(db, cursor, *args, **kwargs)
                return res
            finally:
                if cursor:
                    await cursor.close()
                if db:
                    db.close()
        return wrapper
    return decorate

def serialize_datetime(obj):
    """将对象中的 datetime 字段转换为字符串，用于 JSON 序列化"""
    if isinstance(obj, datetime):
        return obj.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(obj, dict):
        return {key: serialize_datetime(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_datetime(item) for item in obj]
    else:
        return obj