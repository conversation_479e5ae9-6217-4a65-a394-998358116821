-- CREATE TABLE `primary_key` (
--    `create_time` datetime NOT NULL COMMENT "",
--    `fingerprint` varchar(130) NOT NULL COMMENT "",
--    `content` varchar(65533) NOT NULL COMMENT "") 
-- ENGINE=OLAP PRIMARY KEY(`create_time`, `fingerprint`) 
-- COMMENT "OLAP" 
-- DISTRIBUTED BY HASH(`fingerprint`) BUCKETS 32 
-- PROPERTIES (
--    "replication_num" = "3",
-- );

CREATE TABLE `timerange` (
   `timestamp` datetime NOT NULL COMMENT "",
   `meta` varchar(65533) NOT NULL COMMENT ""
)
PRIMARY KEY (`timestamp`, `meta`)
PARTITION BY RANGE(timestamp)(
)
DISTRIBUTED BY HASH(timestamp)
PROPERTIES(
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-300",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "replication_num" = "3"
);

CREATE TABLE `timerange_dk` (
   `timestamp` datetime NOT NULL COMMENT "",
   `meta` varchar(65533) NOT NULL COMMENT ""
)
DUPLICATE KEY (`timestamp`, `meta`)
PARTITION BY RANGE(timestamp)(
)
DISTRIBUTED BY HASH(timestamp)
PROPERTIES(
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-300",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "replication_num" = "3"
);
