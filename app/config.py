"""
Configuration Management - 配置管理
展示函数式编程风格的配置管理

重要原则：
1. 使用环境变量进行配置
2. 提供合理的默认值
3. 配置验证和类型转换
4. 支持不同环境的配置
"""

import os
import uuid
from typing import Dict, Any


def get_env_bool(key: str, default: bool = False) -> bool:
    """获取布尔类型的环境变量"""
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')


def get_env_int(key: str, default: int = 0) -> int:
    """获取整数类型的环境变量"""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default


def get_env_str(key: str, default: str = '') -> str:
    """获取字符串类型的环境变量"""
    return os.getenv(key, default)


# 基础配置 - 函数式风格
base: Dict[str, Any] = {
    # 应用配置
    'APP_NAME': get_env_str('APP_NAME', 'Python API Template'),
    'DEBUG': get_env_bool('DEBUG', True),
    'SECRET_KEY': os.getenv('SECRET_KEY', uuid.uuid4().hex),
    'HOST': get_env_str('HOST', '0.0.0.0'),
    'PORT': get_env_int('PORT', 8000),

    # 数据库配置
    'DB': os.getenv("DB"),  # 保持原有的 DB 配置
    'DB_HOST': get_env_str('DB_HOST', 'localhost'),
    'DB_PORT': get_env_int('DB_PORT', 3306),
    'DB_USER': get_env_str('DB_USER', 'root'),
    'DB_PASSWORD': get_env_str('DB_PASSWORD', ''),
    'DB_NAME': get_env_str('DB_NAME', 'test'),
    'DB_CHARSET': get_env_str('DB_CHARSET', 'utf8mb4'),

    # 连接池配置
    'DB_POOL_MIN_SIZE': get_env_int('DB_POOL_MIN_SIZE', 1),
    'DB_POOL_MAX_SIZE': get_env_int('DB_POOL_MAX_SIZE', 10),

    # 日志配置
    'LOG_LEVEL': get_env_str('LOG_LEVEL', 'INFO'),

    # API 配置
    'API_PREFIX': get_env_str('API_PREFIX', '/api/v1'),
    'DEFAULT_PAGE_SIZE': get_env_int('DEFAULT_PAGE_SIZE', 20),
    'MAX_PAGE_SIZE': get_env_int('MAX_PAGE_SIZE', 1000),
}


# 配置验证函数
def validate_config() -> Dict[str, Any]:
    """验证配置的有效性"""
    errors = []

    # 检查端口范围
    if not (1 <= base['PORT'] <= 65535):
        errors.append("PORT must be between 1 and 65535")

    # 检查连接池配置
    if base['DB_POOL_MIN_SIZE'] > base['DB_POOL_MAX_SIZE']:
        errors.append("DB_POOL_MIN_SIZE cannot be greater than DB_POOL_MAX_SIZE")

    return {
        'valid': len(errors) == 0,
        'errors': errors
    }


# 使用示例
"""
使用示例：

1. 在应用启动时验证配置：
   ```python
   from app.config import validate_config

   validation = validate_config()
   if not validation['valid']:
       for error in validation['errors']:
           print(f"Config Error: {error}")
       exit(1)
   ```

2. 在业务代码中使用配置：
   ```python
   from app.config import base

   page_size = min(request.args.get('limit', base['DEFAULT_PAGE_SIZE']), base['MAX_PAGE_SIZE'])
   ```

3. 环境变量设置示例：
   ```bash
   export DB_HOST=localhost
   export DB_PORT=3306
   export DB_USER=myuser
   export DB_PASSWORD=mypassword
   export DB_NAME=mydb
   export DEBUG=false
   export LOG_LEVEL=INFO
   ```

最佳实践：
1. 所有配置都通过环境变量设置
2. 提供合理的默认值
3. 在应用启动时验证配置
4. 敏感信息（如密码）不要硬编码
5. 使用函数式的配置获取方法
"""