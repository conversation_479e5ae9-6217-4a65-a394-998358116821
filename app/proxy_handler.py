"""
使用方式：
- 在 views.py 中直接从各模块导入函数
- proxy_handler 主要用于模块发现和统一管理
"""

import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入所有模块的 handler
from .timerange import handler as timerange_handler
# 在这里添加更多模块的导入
# from .order_module import handler as order_handler

# Proxy map for organizing handlers by module
proxy_map = {}

# Handler list with module registrations
# 注意：这里注册所有的功能模块
handler_list = [
    {
        "register": "timerange",  # 模块注册名
        "mod": timerange_handler,        # 模块handler对象
    },
    # 添加更多模块注册
    # {
    #     "register": "user_module",
    #     "mod": user_handler,
    # },
    # {
    #     "register": "order_module", 
    #     "mod": order_handler,
    # },
]

# Register all handlers - 注册所有处理器
# 这里使用 Python 的黑魔法来动态注册模块中的所有函数
for handler in handler_list:
    mod, reg = handler["mod"], handler["register"]
    all_attributes = dir(mod)
    if reg not in proxy_map:
        proxy_map[reg] = {}
    for attr in all_attributes:
        item = getattr(mod, attr)
        # 只注册可调用的函数（排除内置属性）
        if callable(item) and not attr.startswith('_'):
            proxy_map[reg][attr] = item
